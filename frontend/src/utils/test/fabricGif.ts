import { fabric } from "fabric";
import { gifToSprite } from "./gifToSprite";
import "../fabric-utils"; // 确保CoverElementBase已加载

/**
 * GIF Crop 功能测试和调试说明
 *
 * 如果GIF crop后显示不正确，请按以下步骤排查：
 *
 * 1. 启用调试模式：
 *    在浏览器控制台中运行: (window as any).DEBUG_GIF_CROP = true;
 *
 * 2. 检查调试输出：
 *    - originalSize: GIF单个帧的原始尺寸
 *    - displaySize: Fabric.js对象的显示尺寸
 *    - cropParams: 计算得出的crop参数
 *    - DrawImage params: 实际传递给drawImage的参数
 *
 * 3. 常见问题：
 *    - 如果originalSize不正确，检查getOriginalSize方法
 *    - 如果cropParams异常，检查getCrop方法的宽高比计算
 *    - 如果sourceX超出范围，检查frameWidth计算
 *
 * 4. 手动设置crop参数进行测试：
 *    gif.cropX = 10;
 *    gif.cropY = 10;
 *    gif.cropWidth = 100;
 *    gif.cropHeight = 100;
 *    gif.canvas.requestRenderAll();
 */

const [PLAY, PAUSE, STOP] = [0, 1, 2];

interface GifResult {
  error?: string;
  play?: () => void;
  pause?: () => void;
  stop?: () => void;
  getStatus?: () => string;
}

interface AnimatedGifImage extends fabric.Image {
  play: () => void;
  pause: () => void;
  stop: () => void;
  getStatus: () => string;
  frameWidth: number;
  frameHeight: number;
  framesLength: number;
  delay: number;
  animationId?: number;
  updateFrame?: (currentTime: number) => boolean;
  destroy?: () => void;
  dispose: () => void;
  // 继承CoverElementBase的边框和剪切属性
  imageBorderColor?: string;
  borderWidth?: number;
  borderStyle?: string;
  borderRadius?: number;
  cropX?: number;
  cropY?: number;
  cropWidth?: number;
  cropHeight?: number;
  disableCrop?: boolean;
  setBorder?: (color: string, width: number, style?: string) => void;
  _renderBorder?: (ctx: CanvasRenderingContext2D, path: Path2D) => void;
  _createClipPath?: () => Path2D;
  getOriginalSize: () => { width: number; height: number };
  getCrop: (
    image: { width: number; height: number },
    size: { width: number; height: number }
  ) => { cropX: number; cropY: number; cropWidth: number; cropHeight: number };
  getCompositeFilter: () => string;
  _renderWithCrop: (ctx: CanvasRenderingContext2D) => void;
  _renderGifFrame: (ctx: CanvasRenderingContext2D) => void;
  // 缓存相关属性
  _cachedClipPath?: Path2D | null;
  _lastWidth?: number;
  _lastHeight?: number;
  _lastRadius?: number;
  _cachedOriginalSize?: { width: number; height: number } | null;
  // Fabric.js标准属性
  imageFlipX?: boolean;
  imageFlipY?: boolean;
  brightness?: number;
  contrast?: number;
  saturation?: number;
  hue?: number;
  blur?: number;
  customFilter?: string;
}

// 全局动画管理器，统一管理所有GIF动画
class GifAnimationManager {
  private static instance: GifAnimationManager;
  private animatedGifs = new Set<AnimatedGifImage>();
  private animationFrameId: number | null = null;
  private lastFrameTime = 0;

  static getInstance(): GifAnimationManager {
    if (!GifAnimationManager.instance) {
      GifAnimationManager.instance = new GifAnimationManager();
    }
    return GifAnimationManager.instance;
  }

  addGif(gif: AnimatedGifImage) {
    this.animatedGifs.add(gif);
    this.startAnimation();
  }

  removeGif(gif: AnimatedGifImage) {
    this.animatedGifs.delete(gif);
    if (this.animatedGifs.size === 0) {
      this.stopAnimation();
    }
  }

  private startAnimation() {
    if (this.animationFrameId) return;

    const animate = (currentTime: number) => {
      // 使用时间间隔控制帧率，避免过度渲染
      if (currentTime - this.lastFrameTime >= 16) {
        // 约60fps
        this.updateGifs(currentTime);
        this.lastFrameTime = currentTime;
      }

      if (this.animatedGifs.size > 0) {
        this.animationFrameId = requestAnimationFrame(animate);
      }
    };

    this.animationFrameId = requestAnimationFrame(animate);
  }

  private stopAnimation() {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  private updateGifs(currentTime: number) {
    let needsRender = false;

    this.animatedGifs.forEach((gif) => {
      if (
        gif.getStatus() === "Playing" &&
        gif.updateFrame &&
        gif.updateFrame(currentTime)
      ) {
        needsRender = true;
      }
    });

    // 批量渲染，减少重绘次数
    if (needsRender) {
      this.animatedGifs.forEach((gif) => {
        if (gif.canvas) {
          gif.dirty = true;
        }
      });
    }
  }

  cleanup() {
    this.stopAnimation();
    this.animatedGifs.clear();
  }
}

/**
 * 优化的fabricGif函数
 * @param gif 可以是URL、dataURL或File对象
 * @param maxWidth 可选，缩放到最大宽度
 * @param maxHeight 可选，缩放到最大高度
 * @param maxDuration 可选，以毫秒为单位减少GIF帧到最大持续时间
 * @returns 错误对象或带有播放控制方法的fabric.Image实例
 */
export const fabricGif = async (
  gif: string | File,
  maxWidth?: number,
  maxHeight?: number,
  maxDuration?: number
): Promise<AnimatedGifImage | GifResult> => {
  try {
    const { error, dataUrl, delay, frameWidth, framesLength } =
      await gifToSprite(gif, maxWidth, maxHeight, maxDuration);

    if (error) return { error } as any;

    return new Promise((resolve, reject) => {
      fabric.Image.fromURL(dataUrl, (img: fabric.Image) => {
        if (!img) {
          reject(new Error("Failed to create fabric image"));
          return;
        }

        const sprite = img.getElement() as HTMLImageElement;
        let framesIndex = 0;
        let lastFrameTime = 0;
        let status: number = PAUSE;

        // 将普通的fabric.Image转换为具有CoverElementBase功能的对象
        const animatedGif = img as AnimatedGifImage;

        // 添加CoverElementBase的默认属性
        animatedGif.imageBorderColor = "transparent";
        animatedGif.borderWidth = 0;
        animatedGif.borderStyle = "solid";
        animatedGif.borderRadius = 0;
        animatedGif.disableCrop = false;

        // 缓存相关属性
        animatedGif._cachedClipPath = null;
        animatedGif._lastWidth = 0;
        animatedGif._lastHeight = 0;
        animatedGif._lastRadius = 0;
        animatedGif._cachedOriginalSize = null;

        // 添加getOriginalSize方法 - 与CoverElementBase保持一致
        animatedGif.getOriginalSize = function () {
          if (this._cachedOriginalSize) {
            return this._cachedOriginalSize;
          }

          // 对于GIF，原始尺寸应该是单个帧的尺寸，而不是整个sprite的尺寸
          let size = { width: frameWidth, height: sprite.naturalHeight };

          // 确保我们使用的是单个帧的实际尺寸
          this._cachedOriginalSize = size;
          return size;
        };

        // 添加getCrop方法 - 与CoverElementBase保持一致
        animatedGif.getCrop = function (
          image: { width: number; height: number },
          size: { width: number; height: number }
        ) {
          if (
            this.cropX !== undefined &&
            this.cropY !== undefined &&
            this.cropWidth &&
            this.cropHeight
          ) {
            return {
              cropX: this.cropX,
              cropY: this.cropY,
              cropWidth: this.cropWidth,
              cropHeight: this.cropHeight,
            };
          }

          const aspectRatio = size.width / size.height;
          const imageRatio = image.width / image.height;

          const { newWidth, newHeight } =
            aspectRatio >= imageRatio
              ? { newWidth: image.width, newHeight: image.width / aspectRatio }
              : {
                  newWidth: image.height * aspectRatio,
                  newHeight: image.height,
                };

          return {
            cropX: (image.width - newWidth) / 2,
            cropY: (image.height - newHeight) / 2,
            cropWidth: newWidth,
            cropHeight: newHeight,
          };
        };

        // 添加边框设置方法
        animatedGif.setBorder = function (
          color: string,
          width: number,
          style: string = "solid"
        ) {
          if (!color) {
            console.error("Invalid border color");
            return;
          }
          this.imageBorderColor = color;
          this.borderWidth = width;
          this.borderStyle = style;
          this.canvas?.requestRenderAll();
        };

        // 添加边框渲染方法
        animatedGif._renderBorder = function (
          ctx: CanvasRenderingContext2D,
          path: Path2D
        ) {
          if (this.borderWidth <= 0) return;

          ctx.strokeStyle = this.imageBorderColor;
          ctx.lineWidth = this.borderWidth;

          const dashArray =
            this.borderStyle === "dashed"
              ? [this.borderWidth * 2, this.borderWidth]
              : this.borderStyle === "dotted"
              ? [this.borderWidth, this.borderWidth]
              : [];

          ctx.setLineDash(dashArray);
          ctx.stroke(path);
          ctx.setLineDash([]);
        };

        // 添加剪切路径创建方法 - 与CoverElementBase保持一致
        animatedGif._createClipPath = function () {
          const { width, height } = this;
          const r = this.borderRadius;

          if (
            this._cachedClipPath &&
            this._lastWidth === width &&
            this._lastHeight === height &&
            this._lastRadius === r
          ) {
            return this._cachedClipPath;
          }

          const path = new Path2D();
          const halfWidth = width / 2;
          const halfHeight = height / 2;

          path.moveTo(-halfWidth + r, -halfHeight);
          path.arcTo(halfWidth, -halfHeight, halfWidth, halfHeight, r);
          path.arcTo(halfWidth, halfHeight, -halfWidth, halfHeight, r);
          path.arcTo(-halfWidth, halfHeight, -halfWidth, -halfHeight, r);
          path.arcTo(-halfWidth, -halfHeight, halfWidth, -halfHeight, r);
          path.closePath();

          this._cachedClipPath = path;
          this._lastWidth = width;
          this._lastHeight = height;
          this._lastRadius = r;

          return path;
        };

        // 添加getCompositeFilter方法 - 与CoverElementBase保持一致
        animatedGif.getCompositeFilter = function () {
          const filters = [
            `brightness(${100 + (this.brightness || 0)}%)`,
            `contrast(${100 + (this.contrast || 0)}%)`,
            `saturate(${100 + (this.saturation || 0)}%)`,
            `hue-rotate(${this.hue || 0}deg)`,
            `blur(${this.blur || 0}px)`,
          ];

          // 添加效果类型滤镜
          const effectType = this.customFilter;
          if (effectType && effectType !== "none") {
            switch (effectType) {
              case "blackAndWhite":
                filters.push("grayscale(100%)");
                break;
              case "sepia":
                filters.push("sepia(100%)");
                break;
              case "invert":
                filters.push("invert(100%)");
                break;
              case "saturate":
                filters.push("saturate(200%)");
                break;
              case "retro":
                filters.push("sepia(50%) contrast(120%) brightness(110%)");
                break;
            }
          }

          return (
            filters
              .filter((f) => f !== "none" && !f.includes("NaN"))
              .join(" ") || "none"
          );
        };

        // 添加统一的_renderWithCrop方法 - 与CoverElementBase保持一致
        animatedGif._renderWithCrop = function (ctx: CanvasRenderingContext2D) {
          if (this.disableCrop) {
            return this._renderGifFrame(ctx);
          }

          const { width, height } = this;
          const originalSize = this.getOriginalSize();
          const { cropX, cropY, cropWidth, cropHeight } = this.getCrop(
            originalSize,
            {
              width: this.getScaledWidth(),
              height: this.getScaledHeight(),
            }
          );

          // 调试日志（可以在开发时启用）
          if ((window as any).DEBUG_GIF_CROP) {
            console.log("GIF Crop Debug:", {
              originalSize,
              displaySize: {
                width: this.getScaledWidth(),
                height: this.getScaledHeight(),
              },
              cropParams: { cropX, cropY, cropWidth, cropHeight },
              frameWidth,
              currentFrame: framesIndex,
            });
          }

          ctx.save();

          if (this.imageFlipX) ctx.scale(-1, 1);
          if (this.imageFlipY) ctx.scale(1, -1);

          const clipPath = this._createClipPath();
          ctx.clip(clipPath);

          const filter = this.getCompositeFilter();
          ctx.filter = filter;

          // GIF特有的帧绘制逻辑
          // 关键修复：正确计算源坐标
          const sourceX = frameWidth * framesIndex + Math.max(cropX, 0); // 当前帧的起始X + 裁剪偏移
          const sourceY = Math.max(cropY, 0); // 裁剪Y偏移
          const sourceWidth = Math.min(
            cropWidth,
            frameWidth - Math.max(cropX, 0)
          ); // 确保不超出帧边界
          const sourceHeight = Math.min(
            cropHeight,
            sprite.naturalHeight - Math.max(cropY, 0)
          ); // 确保不超出图片边界

          // 更多调试信息
          if ((window as any).DEBUG_GIF_CROP) {
            console.log("GIF DrawImage params:", {
              sourceX,
              sourceY,
              sourceWidth,
              sourceHeight,
              destX: -width / 2,
              destY: -height / 2,
              destWidth: width,
              destHeight: height,
            });
          }

          ctx.drawImage(
            sprite,
            sourceX, // 源X：当前帧位置 + crop偏移
            sourceY, // 源Y：crop偏移
            Math.max(1, sourceWidth), // 源宽度：裁剪后的宽度
            Math.max(1, sourceHeight), // 源高度：裁剪后的高度
            -width / 2, // 目标X
            -height / 2, // 目标Y
            width, // 目标宽度
            height // 目标高度
          );

          ctx.filter = "none";

          this._renderBorder(ctx, clipPath);

          ctx.restore();
        };

        // 添加简单的帧渲染方法（不使用crop时）
        animatedGif._renderGifFrame = function (ctx: CanvasRenderingContext2D) {
          ctx.save();

          if (this.imageFlipX) ctx.scale(-1, 1);
          if (this.imageFlipY) ctx.scale(1, -1);

          const clipPath = this._createClipPath();
          if (clipPath) {
            ctx.clip(clipPath);
          }

          const filter = this.getCompositeFilter();
          ctx.filter = filter;

          // 绘制当前帧
          ctx.drawImage(
            sprite,
            frameWidth * framesIndex,
            0,
            frameWidth,
            sprite.height,
            -this.width / 2,
            -this.height / 2,
            this.width,
            this.height
          );

          ctx.filter = "none";

          if (this._renderBorder && clipPath) {
            this._renderBorder(ctx, clipPath);
          }

          ctx.restore();
        };

        // 保存帧信息
        animatedGif.frameWidth = frameWidth;
        animatedGif.frameHeight = sprite.naturalHeight;
        animatedGif.framesLength = framesLength;
        animatedGif.delay = delay;

        // 初始化滤镜属性
        (animatedGif as any).brightness = 0;
        (animatedGif as any).contrast = 0;
        (animatedGif as any).saturation = 0;
        (animatedGif as any).hue = 0;
        (animatedGif as any).blur = 0;
        (animatedGif as any).customFilter = "none";

        // 优化的帧更新函数
        animatedGif.updateFrame = function (currentTime: number): boolean {
          if (status !== PLAY) return false;

          if (currentTime - lastFrameTime >= delay) {
            lastFrameTime = currentTime;
            framesIndex = (framesIndex + 1) % framesLength;
            return true; // 需要重绘
          }
          return false;
        };

        // 新增：根据时间轴位置设置GIF帧的函数
        (animatedGif as any).setFrameByTime = function (
          timelineTime: number,
          elementStartTime: number
        ): boolean {
          if (framesLength <= 1) return false;

          // 计算相对于元素开始时间的时间偏移（毫秒）
          const relativeTime = Math.max(0, timelineTime - elementStartTime);

          // 计算应该显示的帧索引
          // 假设GIF以其原始帧率循环播放
          const cycleTime = framesLength * delay; // 一个完整循环的时间
          const timeInCycle = relativeTime % cycleTime; // 当前循环中的时间
          const targetFrameIndex =
            Math.floor(timeInCycle / delay) % framesLength;

          // 如果帧索引发生变化，更新并返回true表示需要重绘
          if (targetFrameIndex !== framesIndex) {
            framesIndex = targetFrameIndex;
            // 标记对象为脏，强制重绘
            this.dirty = true;
            return true;
          }
          return false;
        };

        // 修改渲染函数，使用统一的crop逻辑
        animatedGif._render = function (ctx: CanvasRenderingContext2D) {
          this._renderWithCrop(ctx);
        };

        // 播放控制方法
        animatedGif.play = function () {
          if (status !== PLAY) {
            status = PLAY;
            lastFrameTime = performance.now();
            GifAnimationManager.getInstance().addGif(this);
            this.dirty = true;
          }
        };

        animatedGif.pause = function () {
          if (status === PLAY) {
            status = PAUSE;
            GifAnimationManager.getInstance().removeGif(this);
            this.dirty = false;
          }
        };

        animatedGif.stop = function () {
          status = STOP;
          framesIndex = 0;
          GifAnimationManager.getInstance().removeGif(this);
          this.dirty = true;
        };

        animatedGif.getStatus = () => ["Playing", "Paused", "Stopped"][status];

        // 重写销毁方法，确保清理资源
        const originalDestroy = animatedGif.destroy;
        animatedGif.destroy = function () {
          GifAnimationManager.getInstance().removeGif(this);
          if (originalDestroy) {
            originalDestroy.call(this);
          }
        };

        // 添加dispose方法来清理缓存
        animatedGif.dispose = function () {
          this._cachedClipPath = null;
          this._cachedOriginalSize = null;
          this._lastWidth = 0;
          this._lastHeight = 0;
          this._lastRadius = 0;
          GifAnimationManager.getInstance().removeGif(this);
          // 调用父类的dispose方法（如果存在）
          if (fabric.Image.prototype.dispose) {
            fabric.Image.prototype.dispose.call(this);
          }
        };

        // 默认暂停状态，只有在时间轴播放或scrubbing时才播放
        // animatedGif.play(); // 移除默认播放
        resolve(animatedGif);
      });
    });
  } catch (error) {
    console.error("fabricGif error:", error);
    return { error: error.message || "Unknown error" };
  }
};

// 清理函数
export const cleanupGifAnimations = () => {
  GifAnimationManager.getInstance().cleanup();
};

// 获取当前活动的GIF数量（用于调试）
export const getActiveGifCount = () => {
  return GifAnimationManager.getInstance()["animatedGifs"].size;
};
